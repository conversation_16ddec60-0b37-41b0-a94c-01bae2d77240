# Edmond Trash Valet - Website

## Overview

This is a professional multi-page website for a trash collection service company called "Edmond Trash Valet." The site provides a can-to-curb service where the company handles moving trash cans from customers' homes to the curb. The website is built using vanilla HTML, CSS, and JavaScript with a modern, clean design focused on local service marketing and comprehensive customer information.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Technology Stack**: Vanilla HTML5, CSS3, and JavaScript
- **Architecture Pattern**: Multi-page website with dedicated pages for different content sections
- **Page Structure**: Home page with service overview, separate About, Contact, Terms, and Privacy pages
- **Responsive Design**: Mobile-first approach with hamburger menu for mobile devices
- **External Dependencies**: Font Awesome for icons, minimal external resources

### Design Philosophy
- Clean, professional design focused on trust and reliability
- Local business branding with emphasis on convenience
- User-friendly booking flow with conditional form fields
- Accessibility considerations with semantic HTML structure

## Key Components

### Navigation System
- Fixed navigation bar with company logo and branding
- Responsive hamburger menu for mobile devices
- Multi-page navigation linking to separate HTML files
- Active state management for current page identification

### Home Page Sections
- **Hero Section**: Prominent call-to-action for service booking with clear value proposition
- **How It Works**: Step-by-step process explanation with numbered visual flow
- **Service Features**: Benefits and reasons to choose the service
- **Promotional Ads**: Visual marketing materials showcasing service advantages
- **Testimonials**: Customer feedback and social proof
- **Booking Form**: Comprehensive service request form with conditional fields
- **Contact Information**: Basic contact details and service area

### Form Handling
- Dynamic form behavior with conditional field display
- Gate code input that appears/disappears based on user selection
- Form validation and required field management
- Clean user experience with progressive disclosure

### Interactive Features
- Mobile menu toggle functionality
- Smooth scrolling for internal navigation
- Form state management
- Responsive design breakpoints

## Data Flow

### User Interaction Flow
1. **Landing**: Users arrive at hero section with clear value proposition
2. **Navigation**: Users can navigate to different sections via smooth scrolling
3. **Booking**: Users fill out service booking form with conditional fields
4. **Contact**: Users can access contact information and service details

### Form Processing
- Client-side form validation
- Conditional field display based on user selections
- Form data collection (gate code handling)
- Currently set up for client-side processing (no backend integration)

## External Dependencies

### Third-Party Services
- **Font Awesome**: Icon library via CDN for UI elements
- **Web Fonts**: System fonts with fallback stack for performance

### Assets
- SVG image assets stored in `/images/` directory
- Company logo (logo.svg), local company badge (local-company.svg)
- Promotional advertisements (ad-blue.svg, ad-secondary.svg)
- Minimal external resource dependencies for fast loading
- Scalable vector graphics for crisp display on all devices

## Deployment Strategy

### Current Setup
- **Static Website**: Designed for simple web hosting
- **File Structure**: Flat file organization suitable for any web server
- **Assets**: Self-contained with minimal external dependencies
- **Performance**: Optimized for fast loading with minimal HTTP requests

### Hosting Requirements
- Basic web hosting with HTML/CSS/JS support
- No server-side processing required for current implementation
- Standard web server configuration
- Multi-page structure ready for static hosting
- Optional CDN for improved global performance

### Scalability Considerations
- Ready for backend integration if booking system needs server processing
- Form structure prepared for API integration
- Modular CSS and JavaScript for easy expansion
- SEO-optimized structure with meta tags and semantic markup

## Page Structure

### Home Page (index.html)
- Complete service overview with hero, how-it-works, features, testimonials, and booking
- Integrated promotional advertising sections
- Comprehensive booking form with conditional field display

### About Page (about.html)
- Company story and mission
- Service process explanation
- Company values and local focus
- Visual content grid layout

### Contact Page (contact.html)
- Contact information with business hours
- Contact form for general inquiries
- FAQ section with common questions
- Service area information

### Legal Pages
- **Terms & Conditions** (terms.html): Service agreement and liability terms
- **Privacy Policy** (privacy.html): Data collection and usage policies

## Technical Implementation Notes

### Form Architecture
- Gate code field visibility controlled by radio button selection
- Progressive enhancement approach for JavaScript functionality
- Graceful degradation for users without JavaScript enabled
- Auto-save functionality with localStorage
- Real-time validation and formatting

### Mobile Responsiveness
- Hamburger menu implementation for mobile navigation
- Touch-friendly interface elements
- Responsive grid system for various screen sizes

### Performance Optimization
- Minimal external dependencies (only Font Awesome)
- Efficient CSS organization with mobile-first responsive design
- Lightweight JavaScript implementation with progressive enhancement
- SVG graphics for scalable, fast-loading images
- Optimized for fast initial page load across all pages

## Recent Changes (January 2025)

### Multi-Page Architecture Implementation
- Converted from single-page to multi-page website structure
- Created dedicated About, Contact, Terms, and Privacy pages
- Enhanced navigation system with proper page linking
- Added comprehensive footer with legal page links

### Content Enhancement
- Added "How It Works" section with step-by-step process visualization
- Integrated customer testimonials section for social proof
- Enhanced service feature descriptions
- Added FAQ section on contact page

### Form Improvements  
- Enhanced booking form with better organization and validation
- Added contact form on dedicated contact page
- Improved field grouping and user experience
- Maintained conditional field display functionality

### Visual Design Updates
- Created professional SVG assets for logo and promotional materials
- Implemented consistent black, white, and gray color scheme
- Added hover effects and smooth transitions
- Enhanced mobile responsiveness across all pages

### Legal Compliance
- Added comprehensive Terms & Conditions page
- Created detailed Privacy Policy
- Implemented proper legal footer links
- Ensured business compliance structure